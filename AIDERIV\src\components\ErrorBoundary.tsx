import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Bug, Co<PERSON> } from 'lucide-react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId: string;
}

const serializeError = (error: any) => {
  if (error instanceof Error) {
    return error.message + '\n' + error.stack;
  }
  return JSON.stringify(error, null, 2);
};

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      errorId: this.generateErrorId()
    };
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);

    // Log error to external service (if configured)
    this.logError(error, errorInfo);

    this.setState({ error, errorInfo });
  }

  private logError(error: Error, errorInfo: ErrorInfo) {
    // In a real app, you'd send this to an error tracking service
    const errorReport = {
      id: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    console.error('Error Report:', errorReport);

    // Store in localStorage for debugging
    try {
      const existingErrors = JSON.parse(localStorage.getItem('errorReports') || '[]');
      existingErrors.push(errorReport);
      localStorage.setItem('errorReports', JSON.stringify(existingErrors.slice(-10))); // Keep last 10
    } catch (e) {
      console.error('Failed to store error report:', e);
    }
  }

  private copyErrorToClipboard = () => {
    const errorText = `Error ID: ${this.state.errorId}
Error: ${this.state.error?.message}
Stack: ${this.state.error?.stack}
Component Stack: ${this.state.errorInfo?.componentStack}
Timestamp: ${new Date().toISOString()}
URL: ${window.location.href}`;

    navigator.clipboard.writeText(errorText).then(() => {
      alert('Error details copied to clipboard');
    });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
          <div className="bg-gray-800 border border-red-500 rounded-lg p-6 max-w-2xl w-full">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-8 h-8 text-red-500 mr-3" />
              <h1 className="text-xl font-bold text-white">Application Error</h1>
            </div>

            <p className="text-gray-300 mb-4">
              The application encountered an unexpected error. This has been logged for investigation.
            </p>

            <div className="bg-gray-900 p-3 rounded mb-4">
              <p className="text-sm text-gray-400">Error ID: <span className="text-red-400 font-mono">{this.state.errorId}</span></p>
            </div>

            {this.state.error && (
              <details className="mb-4">
                <summary className="text-red-400 cursor-pointer mb-2 flex items-center">
                  <Bug className="w-4 h-4 mr-1" />
                  Technical Details
                </summary>
                <pre className="bg-gray-900 p-3 rounded text-sm text-red-300 overflow-auto max-h-40">
                  {serializeError(this.state.error)}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}

            <div className="flex gap-3">
              <button
                onClick={() => window.location.reload()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors flex items-center"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reload Application
              </button>

              <button
                onClick={this.copyErrorToClipboard}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors flex items-center"
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy Error Details
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}