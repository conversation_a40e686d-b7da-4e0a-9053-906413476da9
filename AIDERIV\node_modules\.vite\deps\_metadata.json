{"hash": "5ac2fa95", "configHash": "13419ae4", "lockfileHash": "7f7ea8ba", "browserHash": "9c145d1f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "6cedfddb", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "34797710", "needsInterop": true}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "82e7575d", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "9fb84eef", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9fc5351a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "d702592e", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "e6cd55fe", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "377c70e6", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "dc9c2e02", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0033a7df", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "40b5d881", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "66b73ff2", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "16af776b", "needsInterop": false}}, "chunks": {"chunk-NXESFFTV": {"file": "chunk-NXESFFTV.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}