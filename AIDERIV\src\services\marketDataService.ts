import { getDerivAPI } from './derivAPI';
import { MarketData, ChartData, MarketSymbol, TickData } from '../types/trading';

export interface MarketInfo {
  symbol: string;
  display_name: string;
  market: string;
  market_display_name: string;
  submarket: string;
  submarket_display_name: string;
  pip: number;
  exchange_is_open: number;
  is_trading_suspended: number;
  spot: number;
  spot_age: number;
  spot_time: number;
  quote: {
    ask: number;
    bid: number;
    spread: number;
  };
}

export interface MarketCondition {
  symbol: string;
  trend: 'bullish' | 'bearish' | 'sideways';
  volatility: 'low' | 'medium' | 'high';
  volume: 'low' | 'medium' | 'high';
  support: number;
  resistance: number;
  momentum: number;
  rsi: number;
  macd: {
    value: number;
    signal: number;
    histogram: number;
  };
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
    position: 'upper' | 'middle' | 'lower';
  };
  marketSentiment: 'bullish' | 'bearish' | 'neutral';
  tradingSession: 'asian' | 'european' | 'american' | 'overlap';
  newsImpact: 'low' | 'medium' | 'high';
}

export interface PriceAlert {
  id: string;
  symbol: string;
  type: 'price_above' | 'price_below' | 'change_percent' | 'volatility';
  threshold: number;
  currentValue: number;
  triggered: boolean;
  timestamp: number;
  message: string;
}

export class MarketDataService {
  private derivAPI = getDerivAPI();
  private marketInfo: Map<string, MarketInfo> = new Map();
  private priceHistory: Map<string, number[]> = new Map();
  private volumeHistory: Map<string, number[]> = new Map();
  private priceAlerts: PriceAlert[] = [];
  private subscribers: Map<string, Set<(data: MarketData) => void>> = new Map();
  private conditionSubscribers: Map<string, Set<(condition: MarketCondition) => void>> = new Map();

  constructor() {
    this.initializeMarketData();
  }

  private async initializeMarketData(): Promise<void> {
    try {
      const symbols = await this.derivAPI.getActiveSymbols();
      symbols.forEach(symbol => {
        this.marketInfo.set(symbol.symbol, {
          symbol: symbol.symbol,
          display_name: symbol.display_name,
          market: symbol.market,
          market_display_name: symbol.market_display_name,
          submarket: symbol.submarket,
          submarket_display_name: symbol.submarket_display_name,
          pip: symbol.pip,
          exchange_is_open: symbol.exchange_is_open,
          is_trading_suspended: symbol.is_trading_suspended,
          spot: 0,
          spot_age: 0,
          spot_time: 0,
          quote: {
            ask: 0,
            bid: 0,
            spread: 0,
          },
        });
      });
    } catch (error) {
      console.error('Failed to initialize market data:', error);
    }
  }

  public async subscribeToMarket(symbol: string): Promise<void> {
    try {
      // Subscribe to ticks for real-time price updates
      await this.derivAPI.subscribeToTicks(symbol);
      
      // Get historical data for analysis
      const candles = await this.derivAPI.getCandles(symbol, 60, 1000);
      this.updatePriceHistory(symbol, candles);
      
      // Subscribe to OHLC for candle updates
      await this.derivAPI.subscribeToOHLC(symbol, 60);
      
      console.log(`Successfully subscribed to market data for ${symbol}`);
    } catch (error) {
      console.error(`Failed to subscribe to market ${symbol}:`, error);
      throw error;
    }
  }

  public async unsubscribeFromMarket(symbol: string): Promise<void> {
    // Note: In a real implementation, you'd track subscription IDs
    // and unsubscribe from specific subscriptions
    console.log(`Unsubscribed from market data for ${symbol}`);
  }

  public subscribeToMarketUpdates(symbol: string, callback: (data: MarketData) => void): () => void {
    if (!this.subscribers.has(symbol)) {
      this.subscribers.set(symbol, new Set());
    }
    this.subscribers.get(symbol)!.add(callback);

    // Return unsubscribe function
    return () => {
      this.subscribers.get(symbol)?.delete(callback);
    };
  }

  public subscribeToMarketConditions(symbol: string, callback: (condition: MarketCondition) => void): () => void {
    if (!this.conditionSubscribers.has(symbol)) {
      this.conditionSubscribers.set(symbol, new Set());
    }
    this.conditionSubscribers.get(symbol)!.add(callback);

    return () => {
      this.conditionSubscribers.get(symbol)?.delete(callback);
    };
  }

  private updatePriceHistory(symbol: string, candles: ChartData[]): void {
    const prices = candles.map(c => c.close);
    this.priceHistory.set(symbol, prices);
    
    const volumes = candles.map(c => c.volume || 1000);
    this.volumeHistory.set(symbol, volumes);
  }

  public getMarketInfo(symbol: string): MarketInfo | undefined {
    return this.marketInfo.get(symbol);
  }

  public getPriceHistory(symbol: string): number[] {
    return this.priceHistory.get(symbol) || [];
  }

  public getVolumeHistory(symbol: string): number[] {
    return this.volumeHistory.get(symbol) || [];
  }

  public async getMarketCondition(symbol: string): Promise<MarketCondition | null> {
    const prices = this.getPriceHistory(symbol);
    const volumes = this.getVolumeHistory(symbol);
    
    if (prices.length < 50) {
      return null;
    }

    const currentPrice = prices[prices.length - 1];
    const previousPrice = prices[prices.length - 2];
    const priceChange = currentPrice - previousPrice;
    const priceChangePercent = (priceChange / previousPrice) * 100;

    // Calculate technical indicators
    const rsi = this.calculateRSI(prices, 14);
    const macd = this.calculateMACD(prices);
    const bollinger = this.calculateBollingerBands(prices, 20, 2);
    const support = Math.min(...prices.slice(-20));
    const resistance = Math.max(...prices.slice(-20));

    // Determine trend
    const sma20 = this.calculateSMA(prices, 20);
    const sma50 = this.calculateSMA(prices, 50);
    const currentSMA20 = sma20[sma20.length - 1];
    const currentSMA50 = sma50[sma50.length - 1];
    
    let trend: 'bullish' | 'bearish' | 'sideways';
    if (currentSMA20 > currentSMA50 && currentPrice > currentSMA20) {
      trend = 'bullish';
    } else if (currentSMA20 < currentSMA50 && currentPrice < currentSMA20) {
      trend = 'bearish';
    } else {
      trend = 'sideways';
    }

    // Determine volatility
    const volatility = this.calculateVolatility(prices, 20);
    let volatilityLevel: 'low' | 'medium' | 'high';
    if (volatility < 0.01) {
      volatilityLevel = 'low';
    } else if (volatility < 0.03) {
      volatilityLevel = 'medium';
    } else {
      volatilityLevel = 'high';
    }

    // Determine volume level
    const avgVolume = volumes.slice(-20).reduce((a, b) => a + b, 0) / 20;
    const currentVolume = volumes[volumes.length - 1];
    let volumeLevel: 'low' | 'medium' | 'high';
    if (currentVolume < avgVolume * 0.8) {
      volumeLevel = 'low';
    } else if (currentVolume < avgVolume * 1.2) {
      volumeLevel = 'medium';
    } else {
      volumeLevel = 'high';
    }

    // Determine Bollinger Band position
    let bollingerPosition: 'upper' | 'middle' | 'lower';
    if (currentPrice > bollinger.upper) {
      bollingerPosition = 'upper';
    } else if (currentPrice < bollinger.lower) {
      bollingerPosition = 'lower';
    } else {
      bollingerPosition = 'middle';
    }

    // Determine market sentiment
    let marketSentiment: 'bullish' | 'bearish' | 'neutral';
    if (rsi > 70 && trend === 'bullish') {
      marketSentiment = 'bullish';
    } else if (rsi < 30 && trend === 'bearish') {
      marketSentiment = 'bearish';
    } else {
      marketSentiment = 'neutral';
    }

    // Determine trading session
    const now = new Date();
    const hour = now.getUTCHours();
    let tradingSession: 'asian' | 'european' | 'american' | 'overlap';
    if (hour >= 0 && hour < 8) {
      tradingSession = 'asian';
    } else if (hour >= 8 && hour < 16) {
      tradingSession = 'european';
    } else if (hour >= 16 && hour < 24) {
      tradingSession = 'american';
    } else {
      tradingSession = 'overlap';
    }

    const condition: MarketCondition = {
      symbol,
      trend,
      volatility: volatilityLevel,
      volume: volumeLevel,
      support,
      resistance,
      momentum: priceChangePercent,
      rsi,
      macd,
      bollinger: {
        ...bollinger,
        position: bollingerPosition,
      },
      marketSentiment,
      tradingSession,
      newsImpact: 'low', // Would need news API integration
    };

    // Notify subscribers
    this.conditionSubscribers.get(symbol)?.forEach(callback => {
      callback(condition);
    });

    return condition;
  }

  private calculateSMA(prices: number[], period: number): number[] {
    const sma: number[] = [];
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      sma.push(sum / period);
    }
    return sma;
  }

  private calculateRSI(prices: number[], period: number): number {
    if (prices.length < period + 1) return 50;

    const gains: number[] = [];
    const losses: number[] = [];

    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }

    const avgGain = gains.slice(-period).reduce((a, b) => a + b, 0) / period;
    const avgLoss = losses.slice(-period).reduce((a, b) => a + b, 0) / period;

    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  private calculateMACD(prices: number[]): { value: number; signal: number; histogram: number } {
    const ema12 = this.calculateEMA(prices, 12);
    const ema26 = this.calculateEMA(prices, 26);
    
    if (ema12.length === 0 || ema26.length === 0) {
      return { value: 0, signal: 0, histogram: 0 };
    }

    const macdLine = ema12[ema12.length - 1] - ema26[ema26.length - 1];
    const signalLine = macdLine; // Simplified - would need EMA of MACD line
    const histogram = macdLine - signalLine;

    return {
      value: macdLine,
      signal: signalLine,
      histogram,
    };
  }

  private calculateEMA(prices: number[], period: number): number[] {
    if (prices.length === 0) return [];
    
    const multiplier = 2 / (period + 1);
    const ema: number[] = [prices[0]];

    for (let i = 1; i < prices.length; i++) {
      ema.push((prices[i] - ema[i - 1]) * multiplier + ema[i - 1]);
    }

    return ema;
  }

  private calculateBollingerBands(prices: number[], period: number, stdDev: number): {
    upper: number;
    middle: number;
    lower: number;
  } {
    if (prices.length < period) {
      const current = prices[prices.length - 1] || 0;
      return { upper: current, middle: current, lower: current };
    }

    const sma = this.calculateSMA(prices, period);
    const currentSMA = sma[sma.length - 1];
    
    const recentPrices = prices.slice(-period);
    const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - currentSMA, 2), 0) / period;
    const standardDeviation = Math.sqrt(variance);

    return {
      upper: currentSMA + (standardDeviation * stdDev),
      middle: currentSMA,
      lower: currentSMA - (standardDeviation * stdDev),
    };
  }

  private calculateVolatility(prices: number[], period: number): number {
    if (prices.length < period) return 0;

    const returns: number[] = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }

    const recentReturns = returns.slice(-period);
    const avgReturn = recentReturns.reduce((a, b) => a + b, 0) / period;
    const variance = recentReturns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / period;
    
    return Math.sqrt(variance);
  }

  public addPriceAlert(alert: Omit<PriceAlert, 'id' | 'triggered' | 'timestamp'>): string {
    const id = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newAlert: PriceAlert = {
      ...alert,
      id,
      triggered: false,
      timestamp: Date.now(),
    };
    
    this.priceAlerts.push(newAlert);
    return id;
  }

  public removePriceAlert(id: string): boolean {
    const index = this.priceAlerts.findIndex(alert => alert.id === id);
    if (index !== -1) {
      this.priceAlerts.splice(index, 1);
      return true;
    }
    return false;
  }

  public getPriceAlerts(): PriceAlert[] {
    return [...this.priceAlerts];
  }

  public checkPriceAlerts(symbol: string, currentPrice: number): PriceAlert[] {
    const triggeredAlerts: PriceAlert[] = [];
    
    this.priceAlerts.forEach(alert => {
      if (alert.symbol === symbol && !alert.triggered) {
        let shouldTrigger = false;
        
        switch (alert.type) {
          case 'price_above':
            shouldTrigger = currentPrice > alert.threshold;
            break;
          case 'price_below':
            shouldTrigger = currentPrice < alert.threshold;
            break;
          case 'change_percent':
            const prices = this.getPriceHistory(symbol);
            if (prices.length > 1) {
              const previousPrice = prices[prices.length - 2];
              const changePercent = ((currentPrice - previousPrice) / previousPrice) * 100;
              shouldTrigger = Math.abs(changePercent) > alert.threshold;
            }
            break;
          case 'volatility':
            const volatility = this.calculateVolatility(this.getPriceHistory(symbol), 20);
            shouldTrigger = volatility > alert.threshold;
            break;
        }
        
        if (shouldTrigger) {
          alert.triggered = true;
          alert.currentValue = currentPrice;
          triggeredAlerts.push(alert);
        }
      }
    });
    
    return triggeredAlerts;
  }
}

// Singleton instance
let marketDataServiceInstance: MarketDataService | null = null;

export const getMarketDataService = (): MarketDataService => {
  if (!marketDataServiceInstance) {
    marketDataServiceInstance = new MarketDataService();
  }
  return marketDataServiceInstance;
};
