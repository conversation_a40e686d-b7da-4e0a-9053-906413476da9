import { useEffect, useCallback, useRef } from 'react';
import { useTradingStore } from '../store/tradingStore';
import { getDerivAPI } from '../services/derivAPI';
import { getAIService } from '../services/aiService';
import { getMarketDataService } from '../services/marketDataService';
import { getRiskManager } from '../services/riskManager';
import { MarketSymbol, TradingMode, TradeDirection, Trade, AISignal, MarketData, TradeStatus } from '../types/trading';
import toast from 'react-hot-toast';

// Use a Deriv demo app API token - this is the correct app_id for demo accounts
const API_TOKEN = '';  // Empty token means we'll use demo mode without auth
const DEMO_APP_ID = 1089; // Deriv demo app ID

export const useTrading = () => {
  const {
    currentMarket,
    currentMode,
    isConnected,
    settings,
    account,
    trades,        // <-- subscribe to trades for reactive updates
    aiSignals,     // <-- subscribe to AI signals for reactive updates
    setConnectionStatus,
    updateMarketData,
    updateChartData,
    setAccount,
    addTrade,
    updateTrade,
    addAISignal,
    clearOldSignals,
    updateMarketCondition,
  } = useTradingStore();

  const derivAPI = useRef(getDerivAPI());
  const aiService = useRef(getAIService());
  const marketDataService = useRef(getMarketDataService());
  const riskManager = useRef(getRiskManager());
  const signalInterval = useRef<NodeJS.Timeout | null>(null);
  const autoTradingRef = useRef<boolean>(false);
  const marketSubscriptions = useRef<Map<string, () => void>>(new Map());

  // Initialize connection and event handlers
  useEffect(() => {
    const api = derivAPI.current;
    
    api.setEventHandlers({
      onConnectionStatusChange: (connected, message) => {
        setConnectionStatus(connected);
        if (connected) {
          toast.success(message || 'Connected to Deriv API');
          console.log('Connection established, subscribing to market data...');
          // Subscribe to current market
          api.subscribeToTicks(currentMarket)
            .then(() => {
              console.log(`Successfully subscribed to ${currentMarket} ticks`);
              return api.getCandles(currentMarket);
            })
            .then(() => {
              console.log(`Successfully fetched candle data for ${currentMarket}`);
            })
            .catch(error => {
              console.error('Failed to subscribe to market data:', error);
              toast.error('Failed to subscribe to market data');
            });
        } else {
          if (message) {
            console.warn(`Connection status: ${message}`);
            if (message.includes('failed') || message.includes('error')) {
              toast.error(message);
            } else if (message.includes('Reconnecting')) {
              toast.loading(message, { duration: 3000 });
            } else {
              toast.error(message || 'Disconnected from Deriv API');
            }
          } else {
            toast.error('Disconnected from Deriv API');
          }
        }
      },
      
      onMarketDataUpdate: (data: MarketData) => {
        console.log(`Market data update received for ${data.symbol}:`, data);
        updateMarketData(data.symbol, data);

        // Update risk manager with latest trades
        riskManager.current.updateTrades(trades);
        if (account) {
          riskManager.current.updateAccount(account);
        }

        // Check price alerts
        const alerts = marketDataService.current.checkPriceAlerts(data.symbol, data.lastPrice);
        alerts.forEach(alert => {
          toast.success(`Price Alert: ${alert.message}`, { duration: 10000 });
        });

        // Generate AI signals for current market
        if (data.symbol === currentMarket) {
          generateAISignal(data.symbol, data);
        }
      },
      
      onChartDataUpdate: (symbol, data) => {
        updateChartData(symbol, data);
        
        // Update market conditions
        const condition = aiService.current.getMarketCondition(symbol, data);
        if (condition) {
          updateMarketCondition(symbol, condition);
        }
      },
      
      onAccountUpdate: (accountData) => {
        setAccount(accountData);
      },
      
      onTradeUpdate: (trade) => {
        updateTrade(trade.id, trade);
      },
    });

    // The API service now connects automatically upon initialization.

    // Clean up old signals periodically
    const cleanupInterval = setInterval(() => {
      clearOldSignals();
    }, 60000); // Every minute

    return () => {
      api.disconnect();
      if (signalInterval.current) {
        clearInterval(signalInterval.current);
      }
      clearInterval(cleanupInterval);
    };
  }, []);

  // Handle market changes
  useEffect(() => {
    if (isConnected) {
      const api = derivAPI.current;
      const marketService = marketDataService.current;

      // Unsubscribe from previous market
      marketSubscriptions.current.forEach(unsubscribe => unsubscribe());
      marketSubscriptions.current.clear();

      // Subscribe to new market
      const subscribeToMarket = async () => {
        try {
          await marketService.subscribeToMarket(currentMarket);

          // Subscribe to market updates
          const unsubscribeMarket = marketService.subscribeToMarketUpdates(currentMarket, (data) => {
            updateMarketData(data.symbol, data);
          });

          // Subscribe to market conditions
          const unsubscribeConditions = marketService.subscribeToMarketConditions(currentMarket, (condition) => {
            updateMarketCondition(currentMarket, condition);
          });

          marketSubscriptions.current.set(`${currentMarket}_market`, unsubscribeMarket);
          marketSubscriptions.current.set(`${currentMarket}_conditions`, unsubscribeConditions);

          // Get initial market condition
          const condition = await marketService.getMarketCondition(currentMarket);
          if (condition) {
            updateMarketCondition(currentMarket, condition);
          }

        } catch (error) {
          console.error('Failed to subscribe to market:', error);
          toast.error(`Failed to subscribe to ${currentMarket}`);
        }
      };

      subscribeToMarket();
    }
  }, [currentMarket, isConnected]);

  // Auto trading management
  useEffect(() => {
    const currentSettings = settings[currentMode];
    autoTradingRef.current = currentSettings.autoTrading;

    if (currentSettings.autoTrading && currentMode === 'ai') {
      startAutoTrading();
    } else {
      stopAutoTrading();
    }
  }, [settings, currentMode]);

  const generateAISignal = useCallback((symbol: string, marketData: any) => {
    const chartData = useTradingStore.getState().chartData[symbol];
    if (!chartData || chartData.length < 20) return;

    const signal = aiService.current.analyzeMarket(symbol, marketData, chartData);
    if (signal) {
      addAISignal(signal);
      
      // Trigger notification
      if (signal.confidence > 0.8) {
        toast.success(`Strong ${signal.direction.toUpperCase()} signal for ${symbol}`, {
          duration: 5000,
        });
      }
    }
  }, [addAISignal]);

  const startAutoTrading = useCallback(() => {
    if (signalInterval.current) return; // Already running

    signalInterval.current = setInterval(() => {
      const state = useTradingStore.getState();
      const signals = state.aiSignals.filter(s => 
        s.symbol === state.currentMarket && 
        s.confidence > 0.75 &&
        Date.now() - s.timestamp < s.validity * 1000
      );

      if (signals.length > 0 && autoTradingRef.current) {
        const latestSignal = signals[0];
        executeTrade({
          mode: 'ai',
          direction: latestSignal.direction,
          prediction: latestSignal.prediction,
        });
      }
    }, 5000); // Check every 5 seconds

    toast.success('Auto trading started');
  }, []);

  const stopAutoTrading = useCallback(() => {
    if (signalInterval.current) {
      clearInterval(signalInterval.current);
      signalInterval.current = null;
      toast('Auto trading stopped', { icon: 'ℹ️' });
    }
  }, []);

  const executeTrade = useCallback(async (params: {
    mode: TradingMode;
    direction: TradeDirection;
    prediction?: number;
    customStake?: number;
    customDuration?: number;
  }) => {
    if (!isConnected || !account) {
      toast.error('Not connected to trading server');
      return;
    }

    const currentSettings = settings[params.mode];
    let stake = params.customStake || currentSettings.stake;
    const duration = params.customDuration || currentSettings.duration;

    // Risk management checks
    const riskCheck = riskManager.current.shouldAllowTrade(currentSettings);
    if (!riskCheck.allowed) {
      toast.error(`Trade blocked: ${riskCheck.reason}`);
      return;
    }

    // Get position sizing recommendation
    const marketData = getCurrentMarketData();
    if (marketData) {
      const positionSizing = riskManager.current.calculatePositionSizing(
        currentSettings,
        marketData,
        0.7 // Default confidence
      );

      // Use recommended stake if it's lower than user's setting
      if (positionSizing.recommendedStake < stake) {
        stake = positionSizing.recommendedStake;
        toast.info(`Stake adjusted to $${stake.toFixed(2)} based on risk management`);
      }
    }

    // Check balance
    if (account.balance < stake) {
      toast.error('Insufficient balance');
      return;
    }

    try {
      const api = derivAPI.current;

      // Construct temporary trade record for optimistic UI
      const tempTrade: Trade = {
        id: `temp_${Date.now()}_${Math.random().toString(36).slice(2, 6)}`,
        timestamp: Date.now(),
        symbol: currentMarket,
        market: currentMarket,
        type: params.mode,
        direction: params.direction,
        stake,
        duration,
        prediction: params.prediction,
        entryPrice: 0,
        status: TradeStatus.Pending,
      };

      // Optimistically add trade
      addTrade(tempTrade);

      // Send trade to Deriv
      const duration_unit: 't' | 'm' = ['over_under', 'diff_match', 'rise_fall'].includes(params.mode) ? 't' : 'm';

      const placed = await api.placeTrade({
        symbol: currentMarket,
        amount: stake,
        duration: duration,
        duration_unit,
        mode: params.mode,
        direction: params.direction,
        prediction: params.prediction,
      });

      // Update with real info once confirmed
      updateTrade(tempTrade.id, placed);

      toast.success(`${params.direction.toUpperCase()} trade placed`);
    } catch (error) {
      console.error('Trade execution failed:', error);
      toast.error('Trade execution failed');
    }
  }, [isConnected, account, settings, currentMarket, addTrade, updateTrade]);

  const changeMarket = useCallback((market: MarketSymbol) => {
    useTradingStore.getState().setCurrentMarket(market);
  }, []);

  const changeMode = useCallback((mode: TradingMode) => {
    useTradingStore.getState().setCurrentMode(mode);
  }, []);

  const updateTradingSettings = useCallback((mode: TradingMode, updates: Partial<any>) => {
    useTradingStore.getState().updateSettings(mode, updates);
  }, []);

  // Manual trading functions
  const placeManualTrade = useCallback((
    direction: TradeDirection,
    prediction?: number
  ) => {
    executeTrade({
      mode: currentMode,
      direction,
      prediction,
    });
  }, [currentMode, executeTrade]);

  const placeBinaryTrade = useCallback((direction: 'call' | 'put') => {
    const tradeDirection: TradeDirection = direction === 'call' ? 'up' : 'down';
    placeManualTrade(tradeDirection);
  }, [placeManualTrade]);

  const placeDigitTrade = useCallback((
    type: 'over' | 'under' | 'diff' | 'match',
    prediction: number
  ) => {
    placeManualTrade(type as TradeDirection, prediction);
  }, [placeManualTrade]);

  // Get current market data
  const getCurrentMarketData = useCallback(() => {
    const state = useTradingStore.getState();
    return state.marketData[currentMarket];
  }, [currentMarket]);

  const getCurrentChartData = useCallback(() => {
    const state = useTradingStore.getState();
    return state.chartData[currentMarket] || [];
  }, [currentMarket]);

  const getLatestSignals = useCallback((limit = 5) => {
    const state = useTradingStore.getState();
    return state.aiSignals
      .filter(signal => signal.symbol === currentMarket)
      .slice(0, limit);
  }, [currentMarket]);

  const getTradeHistory = useCallback((limit = 20) => {
    const state = useTradingStore.getState();
    return state.trades.slice(0, limit);
  }, []);

  const getTradingStats = useCallback(() => {
    const state = useTradingStore.getState();
    const trades = state.trades;

    const wonTrades = trades.filter(t => t.status === 'won');
    const lostTrades = trades.filter(t => t.status === 'lost');
    const activeTrades = trades.filter(t => t.status === 'active');

    const completedCount = wonTrades.length + lostTrades.length;

    // Calculate P/L only from completed trades (won/lost)
    const totalProfit = wonTrades.reduce((sum, t) => sum + (t.profit ?? 0), 0);
    const totalLoss = lostTrades.reduce((sum, t) => sum + Math.abs(t.profit ?? 0), 0);

    const winRate = completedCount === 0 ? 0 : (wonTrades.length / completedCount) * 100;

    // Get risk metrics
    const riskMetrics = riskManager.current.calculateRiskMetrics();

    return {
      totalTrades: trades.length,
      wonTrades: wonTrades.length,
      lostTrades: lostTrades.length,
      activeTrades: activeTrades.length,
      winRate,
      totalProfit,
      totalLoss,
      netProfit: totalProfit - totalLoss,
      riskMetrics,
    };
  }, []);

  // Enhanced market data functions
  const getMarketInfo = useCallback((symbol: string) => {
    return marketDataService.current.getMarketInfo(symbol);
  }, []);

  const getMarketCondition = useCallback(async (symbol: string) => {
    return await marketDataService.current.getMarketCondition(symbol);
  }, []);

  const addPriceAlert = useCallback((alert: {
    symbol: string;
    type: 'price_above' | 'price_below' | 'change_percent' | 'volatility';
    threshold: number;
    message: string;
  }) => {
    return marketDataService.current.addPriceAlert(alert);
  }, []);

  const removePriceAlert = useCallback((id: string) => {
    return marketDataService.current.removePriceAlert(id);
  }, []);

  const getPriceAlerts = useCallback(() => {
    return marketDataService.current.getPriceAlerts();
  }, []);

  // Risk management functions
  const getRiskMetrics = useCallback(() => {
    return riskManager.current.calculateRiskMetrics();
  }, []);

  const getRiskAlerts = useCallback(() => {
    return riskManager.current.getRiskAlerts();
  }, []);

  const updateRiskSettings = useCallback((settings: {
    maxConsecutiveLosses?: number;
    maxDailyLoss?: number;
    maxDrawdown?: number;
  }) => {
    riskManager.current.updateSettings(settings);
    toast.success('Risk settings updated');
  }, []);

  // Advanced trading functions
  const getPositionSizing = useCallback((confidence: number = 0.7) => {
    const currentSettings = settings[currentMode];
    const marketData = getCurrentMarketData();

    if (!marketData) {
      return null;
    }

    return riskManager.current.calculatePositionSizing(currentSettings, marketData, confidence);
  }, [currentMode, settings]);

  const getContractDetails = useCallback(async (symbol: string) => {
    try {
      return await derivAPI.current.getContractsFor(symbol);
    } catch (error) {
      console.error('Failed to get contract details:', error);
      return null;
    }
  }, []);

  const getProposal = useCallback(async (params: {
    contract_type: string;
    symbol: string;
    duration: number;
    duration_unit: string;
    amount: number;
    barrier?: string;
  }) => {
    try {
      return await derivAPI.current.getProposal({
        ...params,
        basis: 'stake',
        currency: account?.currency || 'USD',
      });
    } catch (error) {
      console.error('Failed to get proposal:', error);
      return null;
    }
  }, [account]);

  const getAccountStatement = useCallback(async (params?: {
    limit?: number;
    offset?: number;
    date_from?: number;
    date_to?: number;
  }) => {
    try {
      return await derivAPI.current.getStatement(params);
    } catch (error) {
      console.error('Failed to get account statement:', error);
      return null;
    }
  }, []);

  const getProfitTable = useCallback(async (params?: {
    limit?: number;
    offset?: number;
    date_from?: number;
    date_to?: number;
  }) => {
    try {
      return await derivAPI.current.getProfitTable(params);
    } catch (error) {
      console.error('Failed to get profit table:', error);
      return null;
    }
  }, []);

  // Connection is now handled automatically by the service.

  const disconnect = useCallback(() => {
    const api = derivAPI.current;
    api.disconnect();
    setConnectionStatus(false);
  }, [setConnectionStatus]);

  return {
    // State
    currentMarket,
    currentMode,
    isConnected,
    settings: settings[currentMode],
    account,

    // Market data
    getCurrentMarketData,
    getCurrentChartData,
    getLatestSignals,
    getMarketInfo,
    getMarketCondition,

    // Trading operations
    executeTrade,
    placeManualTrade,
    placeBinaryTrade,
    placeDigitTrade,
    getPositionSizing,
    getContractDetails,
    getProposal,

    // Configuration
    changeMarket,
    changeMode,
    updateTradingSettings,

    // Auto trading
    startAutoTrading,
    stopAutoTrading,

    // Connection handling
    disconnect,

    // Statistics & Analytics
    getTradeHistory,
    getTradingStats,
    getAccountStatement,
    getProfitTable,

    // Risk Management
    getRiskMetrics,
    getRiskAlerts,
    updateRiskSettings,

    // Price Alerts
    addPriceAlert,
    removePriceAlert,
    getPriceAlerts,
  };
};

export default useTrading;
