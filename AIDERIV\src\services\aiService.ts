import { MarketData, ChartData, AISignal, MarketCondition, TradingMode, TradeDirection } from '../types/trading';

export class AITradingService {
  private priceHistory: Record<string, number[]> = {};
  private digitHistory: Record<string, number[]> = {};
  private patterns: Record<string, any[]> = {};
  private volatilityData: Record<string, number[]> = {};
  private volumeData: Record<string, number[]> = {};
  private marketSentiment: Record<string, number> = {};

  // Enhanced technical indicators cache
  private indicators: Record<string, {
    sma: number[];
    ema: number[];
    rsi: number;
    macd: { macd: number; signal: number; histogram: number };
    bollinger: { upper: number; middle: number; lower: number };
    stochastic: { k: number; d: number };
    williams: number;
    cci: number;
    atr: number;
    adx: number;
    ichimoku: {
      tenkanSen: number;
      kijunSen: number;
      senkouSpanA: number;
      senkouSpanB: number;
      chikouSpan: number;
    };
  }> = {};

  // Machine learning models cache
  private mlModels: Record<string, {
    weights: number[];
    bias: number;
    accuracy: number;
    lastTrained: number;
  }> = {};

  // Pattern recognition cache
  private candlestickPatterns: Record<string, {
    pattern: string;
    strength: number;
    direction: 'bullish' | 'bearish';
    reliability: number;
  }[]> = {};

  constructor() {
    this.initializePatterns();
    this.initializeMLModels();
  }

  private initializePatterns(): void {
    // Initialize common trading patterns and strategies
    this.patterns = {
      bullish: [
        { name: 'Hammer', weight: 0.7 },
        { name: 'Bullish Engulfing', weight: 0.8 },
        { name: 'Morning Star', weight: 0.9 },
        { name: 'Golden Cross', weight: 0.75 }
      ],
      bearish: [
        { name: 'Shooting Star', weight: 0.7 },
        { name: 'Bearish Engulfing', weight: 0.8 },
        { name: 'Evening Star', weight: 0.9 },
        { name: 'Death Cross', weight: 0.75 }
      ],
      neutral: [
        { name: 'Doji', weight: 0.5 },
        { name: 'Spinning Top', weight: 0.4 }
      ]
    };
  }

  private initializeMLModels(): void {
    // Initialize simple neural network weights for each symbol
    const symbols = ['R_100', 'R_50', 'R_25', 'R_10', 'BOOM_1000', 'CRASH_1000'];
    symbols.forEach(symbol => {
      this.mlModels[symbol] = {
        weights: Array(20).fill(0).map(() => Math.random() * 0.1 - 0.05),
        bias: Math.random() * 0.1 - 0.05,
        accuracy: 0.5,
        lastTrained: Date.now(),
      };
    });
  }

  // Main AI analysis function
  analyzeMarket(symbol: string, marketData: MarketData, chartData: ChartData[]): AISignal | null {
    if (!chartData || chartData.length < 20) {
      return null; // Need sufficient data for analysis
    }

    // Update historical data
    this.updateHistory(symbol, marketData, chartData);
    
    // Calculate technical indicators
    this.updateIndicators(symbol, chartData);
    
    // Perform multi-factor analysis
    const analysis = this.performAnalysis(symbol, marketData, chartData);
    
    if (analysis.confidence < 0.6) {
      return null; // Only return signals with sufficient confidence
    }

    return {
      id: `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      symbol,
      direction: analysis.direction,
      confidence: analysis.confidence,
      reason: analysis.reason,
      type: 'ai',
      prediction: analysis.prediction,
      validity: 300 // 5 minutes validity
    };
  }

  private updateHistory(symbol: string, marketData: MarketData, chartData: ChartData[]): void {
    // Update price history
    if (!this.priceHistory[symbol]) {
      this.priceHistory[symbol] = [];
    }
    this.priceHistory[symbol].push(marketData.lastPrice);
    if (this.priceHistory[symbol].length > 1000) {
      this.priceHistory[symbol] = this.priceHistory[symbol].slice(-1000);
    }

    // Update digit history
    if (!this.digitHistory[symbol]) {
      this.digitHistory[symbol] = [];
    }
    this.digitHistory[symbol].push(...marketData.digits);
    if (this.digitHistory[symbol].length > 500) {
      this.digitHistory[symbol] = this.digitHistory[symbol].slice(-500);
    }

    // Update volatility data
    if (!this.volatilityData[symbol]) {
      this.volatilityData[symbol] = [];
    }
    if (chartData.length >= 2) {
      const volatility = Math.abs(chartData[chartData.length - 1].close - chartData[chartData.length - 2].close);
      this.volatilityData[symbol].push(volatility);
      if (this.volatilityData[symbol].length > 100) {
        this.volatilityData[symbol] = this.volatilityData[symbol].slice(-100);
      }
    }
  }

  private updateIndicators(symbol: string, chartData: ChartData[]): void {
    const prices = chartData.map(d => d.close);
    const highs = chartData.map(d => d.high);
    const lows = chartData.map(d => d.low);
    const volumes = chartData.map(d => d.volume || 1000); // Default volume if not available

    this.indicators[symbol] = {
      sma: this.calculateSMA(prices, 14),
      ema: this.calculateEMA(prices, 14),
      rsi: this.calculateRSI(prices, 14),
      macd: this.calculateMACD(prices),
      bollinger: this.calculateBollingerBands(prices, 20, 2),
      stochastic: this.calculateStochastic(highs, lows, prices, 14),
      williams: this.calculateWilliamsR(highs, lows, prices, 14),
      cci: this.calculateCCI(highs, lows, prices, 20),
      atr: this.calculateATR(highs, lows, prices, 14),
      adx: this.calculateADX(highs, lows, prices, 14),
      ichimoku: this.calculateIchimoku(highs, lows, prices)
    };
  }

  private performAnalysis(symbol: string, marketData: MarketData, chartData: ChartData[]): {
    direction: TradeDirection;
    confidence: number;
    reason: string;
    prediction?: number;
  } {
    const indicators = this.indicators[symbol];
    const priceHistory = this.priceHistory[symbol];
    const digitHistory = this.digitHistory[symbol];
    
    let bullishScore = 0;
    let bearishScore = 0;
    let reasons: string[] = [];

    // Technical indicator analysis
    if (indicators) {
      // RSI analysis
      if (indicators.rsi < 30) {
        bullishScore += 0.3;
        reasons.push('RSI oversold');
      } else if (indicators.rsi > 70) {
        bearishScore += 0.3;
        reasons.push('RSI overbought');
      }

      // MACD analysis
      if (indicators.macd.macd > indicators.macd.signal) {
        bullishScore += 0.2;
        reasons.push('MACD bullish crossover');
      } else {
        bearishScore += 0.2;
        reasons.push('MACD bearish crossover');
      }

      // Bollinger Bands analysis
      const currentPrice = marketData.lastPrice;
      if (currentPrice <= indicators.bollinger.lower) {
        bullishScore += 0.25;
        reasons.push('Price at lower Bollinger Band');
      } else if (currentPrice >= indicators.bollinger.upper) {
        bearishScore += 0.25;
        reasons.push('Price at upper Bollinger Band');
      }

      // Stochastic analysis
      if (indicators.stochastic.k < 20 && indicators.stochastic.d < 20) {
        bullishScore += 0.2;
        reasons.push('Stochastic oversold');
      } else if (indicators.stochastic.k > 80 && indicators.stochastic.d > 80) {
        bearishScore += 0.2;
        reasons.push('Stochastic overbought');
      }
    }

    // Pattern recognition analysis
    const patternAnalysis = this.analyzePatterns(chartData);
    bullishScore += patternAnalysis.bullish;
    bearishScore += patternAnalysis.bearish;
    reasons.push(...patternAnalysis.reasons);

    // Digit pattern analysis
    const digitAnalysis = this.analyzeDigitPatterns(digitHistory);
    if (digitAnalysis) {
      reasons.push(digitAnalysis.reason);
    }

    // Volume and momentum analysis
    const momentumAnalysis = this.analyzeMomentum(priceHistory);
    bullishScore += momentumAnalysis.bullish;
    bearishScore += momentumAnalysis.bearish;
    reasons.push(...momentumAnalysis.reasons);

    // Volatility analysis
    const volatilityAnalysis = this.analyzeVolatility(symbol);
    bullishScore += volatilityAnalysis.bullish;
    bearishScore += volatilityAnalysis.bearish;
    reasons.push(...volatilityAnalysis.reasons);

    // Determine final direction and confidence
    const totalScore = bullishScore + bearishScore;
    const confidence = Math.min(Math.abs(bullishScore - bearishScore) / Math.max(totalScore, 1), 0.95);
    
    let direction: TradeDirection;
    if (bullishScore > bearishScore) {
      direction = 'up';
    } else {
      direction = 'down';
    }

    // Add market condition context
    const marketCondition = this.getMarketCondition(symbol, chartData);
    if (marketCondition) {
      reasons.push(`Market trend: ${marketCondition.trend}`);
    }

    return {
      direction,
      confidence,
      reason: reasons.join(', '),
      prediction: digitAnalysis?.prediction
    };
  }

  private analyzePatterns(chartData: ChartData[]): { bullish: number; bearish: number; reasons: string[] } {
    let bullish = 0;
    let bearish = 0;
    const reasons: string[] = [];

    if (chartData.length < 3) return { bullish, bearish, reasons };

    const latest = chartData[chartData.length - 1];
    const previous = chartData[chartData.length - 2];
    const beforePrevious = chartData[chartData.length - 3];

    // Hammer pattern
    if (this.isHammer(latest)) {
      bullish += 0.15;
      reasons.push('Hammer pattern detected');
    }

    // Shooting star pattern
    if (this.isShootingStar(latest)) {
      bearish += 0.15;
      reasons.push('Shooting star pattern detected');
    }

    // Engulfing patterns
    if (this.isBullishEngulfing(previous, latest)) {
      bullish += 0.2;
      reasons.push('Bullish engulfing pattern');
    } else if (this.isBearishEngulfing(previous, latest)) {
      bearish += 0.2;
      reasons.push('Bearish engulfing pattern');
    }

    // Morning/Evening star patterns
    if (this.isMorningStar(beforePrevious, previous, latest)) {
      bullish += 0.25;
      reasons.push('Morning star pattern');
    } else if (this.isEveningStar(beforePrevious, previous, latest)) {
      bearish += 0.25;
      reasons.push('Evening star pattern');
    }

    return { bullish, bearish, reasons };
  }

  private analyzeDigitPatterns(digits: number[]): { prediction: number; reason: string } | null {
    if (digits.length < 20) return null;

    const recentDigits = digits.slice(-20);
    const digitCounts = new Array(10).fill(0);
    
    recentDigits.forEach(digit => digitCounts[digit]++);
    
    // Find least frequent digit (for over/under strategies)
    const leastFrequent = digitCounts.indexOf(Math.min(...digitCounts));
    
    // Analyze sequences
    const sequences = this.findSequences(recentDigits);
    
    if (sequences.length > 0) {
      return {
        prediction: leastFrequent,
        reason: `Digit pattern analysis suggests ${leastFrequent}`
      };
    }

    return null;
  }

  private analyzeMomentum(prices: number[]): { bullish: number; bearish: number; reasons: string[] } {
    let bullish = 0;
    let bearish = 0;
    const reasons: string[] = [];

    if (prices.length < 10) return { bullish, bearish, reasons };

    const recentPrices = prices.slice(-10);
    const momentum = recentPrices[recentPrices.length - 1] - recentPrices[0];
    const avgMomentum = momentum / recentPrices.length;

    if (avgMomentum > 0) {
      bullish += Math.min(avgMomentum * 0.1, 0.2);
      reasons.push('Positive momentum detected');
    } else {
      bearish += Math.min(Math.abs(avgMomentum) * 0.1, 0.2);
      reasons.push('Negative momentum detected');
    }

    return { bullish, bearish, reasons };
  }

  private analyzeVolatility(symbol: string): { bullish: number; bearish: number; reasons: string[] } {
    let bullish = 0;
    let bearish = 0;
    const reasons: string[] = [];

    const volatility = this.volatilityData[symbol];
    if (!volatility || volatility.length < 10) return { bullish, bearish, reasons };

    const avgVolatility = volatility.reduce((sum, v) => sum + v, 0) / volatility.length;
    const recentVolatility = volatility.slice(-5).reduce((sum, v) => sum + v, 0) / 5;

    if (recentVolatility > avgVolatility * 1.5) {
      // High volatility might indicate reversal
      bearish += 0.1;
      reasons.push('High volatility detected');
    } else if (recentVolatility < avgVolatility * 0.5) {
      // Low volatility might indicate accumulation
      bullish += 0.1;
      reasons.push('Low volatility detected');
    }

    return { bullish, bearish, reasons };
  }

  // Technical indicator calculations
  private calculateSMA(prices: number[], period: number): number[] {
    const sma: number[] = [];
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      sma.push(sum / period);
    }
    return sma;
  }

  private calculateEMA(prices: number[], period: number): number[] {
    const ema: number[] = [];
    const multiplier = 2 / (period + 1);
    
    ema[0] = prices[0];
    for (let i = 1; i < prices.length; i++) {
      ema[i] = (prices[i] - ema[i - 1]) * multiplier + ema[i - 1];
    }
    return ema;
  }

  private calculateRSI(prices: number[], period: number): number {
    if (prices.length < period + 1) return 50;

    let gains = 0;
    let losses = 0;

    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) {
        gains += change;
      } else {
        losses -= change;
      }
    }

    const avgGain = gains / period;
    const avgLoss = losses / period;
    const rs = avgGain / avgLoss;
    
    return 100 - (100 / (1 + rs));
  }

  private calculateMACD(prices: number[]): { macd: number; signal: number; histogram: number } {
    const ema12 = this.calculateEMA(prices, 12);
    const ema26 = this.calculateEMA(prices, 26);
    
    if (ema12.length === 0 || ema26.length === 0) {
      return { macd: 0, signal: 0, histogram: 0 };
    }

    const macd = ema12[ema12.length - 1] - ema26[ema26.length - 1];
    const signal = 0; // Simplified for this example
    const histogram = macd - signal;

    return { macd, signal, histogram };
  }

  private calculateBollingerBands(prices: number[], period: number, stdDev: number): { upper: number; middle: number; lower: number } {
    if (prices.length < period) {
      const currentPrice = prices[prices.length - 1];
      return { upper: currentPrice, middle: currentPrice, lower: currentPrice };
    }

    const sma = this.calculateSMA(prices, period);
    const currentSMA = sma[sma.length - 1];
    
    const recentPrices = prices.slice(-period);
    const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - currentSMA, 2), 0) / period;
    const standardDeviation = Math.sqrt(variance);

    return {
      upper: currentSMA + (standardDeviation * stdDev),
      middle: currentSMA,
      lower: currentSMA - (standardDeviation * stdDev)
    };
  }

  private calculateStochastic(highs: number[], lows: number[], closes: number[], period: number): { k: number; d: number } {
    if (closes.length < period) {
      return { k: 50, d: 50 };
    }

    const recentHighs = highs.slice(-period);
    const recentLows = lows.slice(-period);
    const currentClose = closes[closes.length - 1];

    const highestHigh = Math.max(...recentHighs);
    const lowestLow = Math.min(...recentLows);

    const k = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
    const d = k; // Simplified, should be 3-period SMA of %K

    return { k, d };
  }

  // Candlestick pattern recognition
  private isHammer(candle: ChartData): boolean {
    const bodySize = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    
    return lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5;
  }

  private isShootingStar(candle: ChartData): boolean {
    const bodySize = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    
    return upperShadow > bodySize * 2 && lowerShadow < bodySize * 0.5;
  }

  private isBullishEngulfing(prev: ChartData, current: ChartData): boolean {
    return prev.close < prev.open && // Previous candle is bearish
           current.close > current.open && // Current candle is bullish
           current.open < prev.close && // Current opens below previous close
           current.close > prev.open; // Current closes above previous open
  }

  private isBearishEngulfing(prev: ChartData, current: ChartData): boolean {
    return prev.close > prev.open && // Previous candle is bullish
           current.close < current.open && // Current candle is bearish
           current.open > prev.close && // Current opens above previous close
           current.close < prev.open; // Current closes below previous open
  }

  private isMorningStar(first: ChartData, second: ChartData, third: ChartData): boolean {
    return first.close < first.open && // First candle is bearish
           Math.abs(second.close - second.open) < (first.high - first.low) * 0.3 && // Second is doji-like
           third.close > third.open && // Third candle is bullish
           third.close > (first.open + first.close) / 2; // Third closes above midpoint of first
  }

  private isEveningStar(first: ChartData, second: ChartData, third: ChartData): boolean {
    return first.close > first.open && // First candle is bullish
           Math.abs(second.close - second.open) < (first.high - first.low) * 0.3 && // Second is doji-like
           third.close < third.open && // Third candle is bearish
           third.close < (first.open + first.close) / 2; // Third closes below midpoint of first
  }

  private findSequences(digits: number[]): number[][] {
    const sequences: number[][] = [];
    let currentSequence: number[] = [digits[0]];

    for (let i = 1; i < digits.length; i++) {
      if (digits[i] === digits[i - 1]) {
        currentSequence.push(digits[i]);
      } else {
        if (currentSequence.length >= 3) {
          sequences.push([...currentSequence]);
        }
        currentSequence = [digits[i]];
      }
    }

    if (currentSequence.length >= 3) {
      sequences.push(currentSequence);
    }

    return sequences;
  }

  getMarketCondition(symbol: string, chartData: ChartData[]): MarketCondition | null {
    if (chartData.length < 20) return null;

    const prices = chartData.map(d => d.close);
    const sma20 = this.calculateSMA(prices, 20);
    const currentPrice = prices[prices.length - 1];
    const currentSMA = sma20[sma20.length - 1];
    
    // Determine trend
    let trend: 'bullish' | 'bearish' | 'sideways';
    if (currentPrice > currentSMA * 1.02) {
      trend = 'bullish';
    } else if (currentPrice < currentSMA * 0.98) {
      trend = 'bearish';
    } else {
      trend = 'sideways';
    }

    // Calculate volatility
    const volatilityValues = this.volatilityData[symbol] || [];
    const avgVolatility = volatilityValues.reduce((sum, v) => sum + v, 0) / volatilityValues.length;
    let volatility: 'low' | 'medium' | 'high';
    
    if (avgVolatility < currentPrice * 0.001) {
      volatility = 'low';
    } else if (avgVolatility > currentPrice * 0.005) {
      volatility = 'high';
    } else {
      volatility = 'medium';
    }

    // Calculate support and resistance
    const recentPrices = prices.slice(-50);
    const support = Math.min(...recentPrices);
    const resistance = Math.max(...recentPrices);

    // Calculate momentum
    const momentum = prices[prices.length - 1] - prices[prices.length - 10];

    return {
      symbol,
      trend,
      volatility,
      support,
      resistance,
      momentum
    };
  }
  // Advanced Technical Indicators
  private calculateWilliamsR(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < period) return -50;

    const recentHighs = highs.slice(-period);
    const recentLows = lows.slice(-period);
    const currentClose = closes[closes.length - 1];

    const highestHigh = Math.max(...recentHighs);
    const lowestLow = Math.min(...recentLows);

    return ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;
  }

  private calculateCCI(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < period) return 0;

    const typicalPrices = closes.map((close, i) => (highs[i] + lows[i] + close) / 3);
    const smaTP = this.calculateSMA(typicalPrices, period);
    const currentSMA = smaTP[smaTP.length - 1];
    const currentTP = typicalPrices[typicalPrices.length - 1];

    const recentTP = typicalPrices.slice(-period);
    const meanDeviation = recentTP.reduce((sum, tp) => sum + Math.abs(tp - currentSMA), 0) / period;

    return (currentTP - currentSMA) / (0.015 * meanDeviation);
  }

  private calculateATR(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < 2) return 0;

    const trueRanges: number[] = [];
    for (let i = 1; i < closes.length; i++) {
      const tr1 = highs[i] - lows[i];
      const tr2 = Math.abs(highs[i] - closes[i - 1]);
      const tr3 = Math.abs(lows[i] - closes[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }

    const atr = this.calculateSMA(trueRanges, period);
    return atr[atr.length - 1] || 0;
  }

  private calculateADX(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < period + 1) return 0;

    const dmPlus: number[] = [];
    const dmMinus: number[] = [];

    for (let i = 1; i < highs.length; i++) {
      const upMove = highs[i] - highs[i - 1];
      const downMove = lows[i - 1] - lows[i];

      dmPlus.push(upMove > downMove && upMove > 0 ? upMove : 0);
      dmMinus.push(downMove > upMove && downMove > 0 ? downMove : 0);
    }

    const atr = this.calculateATR(highs, lows, closes, period);
    if (atr === 0) return 0;

    const diPlus = this.calculateSMA(dmPlus, period);
    const diMinus = this.calculateSMA(dmMinus, period);

    const currentDIPlus = (diPlus[diPlus.length - 1] / atr) * 100;
    const currentDIMinus = (diMinus[diMinus.length - 1] / atr) * 100;

    const dx = Math.abs(currentDIPlus - currentDIMinus) / (currentDIPlus + currentDIMinus) * 100;
    return dx;
  }

  private calculateIchimoku(highs: number[], lows: number[], closes: number[]): {
    tenkanSen: number;
    kijunSen: number;
    senkouSpanA: number;
    senkouSpanB: number;
    chikouSpan: number;
  } {
    const len = closes.length;
    if (len < 52) {
      const current = closes[len - 1];
      return {
        tenkanSen: current,
        kijunSen: current,
        senkouSpanA: current,
        senkouSpanB: current,
        chikouSpan: current,
      };
    }

    // Tenkan-sen (9-period)
    const tenkanHigh = Math.max(...highs.slice(-9));
    const tenkanLow = Math.min(...lows.slice(-9));
    const tenkanSen = (tenkanHigh + tenkanLow) / 2;

    // Kijun-sen (26-period)
    const kijunHigh = Math.max(...highs.slice(-26));
    const kijunLow = Math.min(...lows.slice(-26));
    const kijunSen = (kijunHigh + kijunLow) / 2;

    // Senkou Span A
    const senkouSpanA = (tenkanSen + kijunSen) / 2;

    // Senkou Span B (52-period)
    const senkouHigh = Math.max(...highs.slice(-52));
    const senkouLow = Math.min(...lows.slice(-52));
    const senkouSpanB = (senkouHigh + senkouLow) / 2;

    // Chikou Span (current close shifted back 26 periods)
    const chikouSpan = closes[len - 26] || closes[len - 1];

    return {
      tenkanSen,
      kijunSen,
      senkouSpanA,
      senkouSpanB,
      chikouSpan,
    };
  }
}

// Singleton instance
let aiServiceInstance: AITradingService | null = null;

export const getAIService = (): AITradingService => {
  if (!aiServiceInstance) {
    aiServiceInstance = new AITradingService();
  }
  return aiServiceInstance;
};
