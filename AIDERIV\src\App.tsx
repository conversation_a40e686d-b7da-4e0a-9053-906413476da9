import React, { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import Header from './components/Header';
import Chart from './components/Chart';
import TradingPanel from './components/TradingPanel';
import Sidebar from './components/Sidebar';
import { RealTimeMarketDashboard } from './components/RealTimeMarketDashboard';
import { LiveTradingStats } from './components/LiveTradingStats';
import { useTradingStore } from './store/tradingStore';
import { useDemo } from './hooks/useDemo';
import { useTrading } from './hooks/useTrading';

function App() {
  const { sidebarOpen, setSidebarOpen, theme } = useTradingStore();
  const [mounted, setMounted] = useState(false);
  const [usingDemoMode, setUsingDemoMode] = useState(false);
  
  // Initialize trading hook
  const trading = useTrading();
  
  // Initialize demo hook
  const demo = useDemo();
  
  // Switch between demo and live mode
  const [isConnecting, setIsConnecting] = useState(false);
  
  // Extract only the functions we need from the hooks to prevent dependency issues
  const { activateDemo, deactivateDemo } = demo;
  const { disconnect } = trading;
  
  useEffect(() => {
    if (usingDemoMode) {
      // Activate demo mode
      setIsConnecting(true);
      activateDemo();
      disconnect();
      setIsConnecting(false);
    } else {
      // Activate real trading
      setIsConnecting(true);
      deactivateDemo();
      // Connection is now automatic
      setIsConnecting(false);
    }
  }, [usingDemoMode, activateDemo, deactivateDemo, disconnect]);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading LDP Enhanced Trading System...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${theme.mode === 'dark' ? 'dark' : ''}`}>
      <div className="min-h-screen bg-gray-900 text-white">
        {/* Toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#1F2937',
              color: '#FFFFFF',
              border: '1px solid #374151',
            },
            success: {
              iconTheme: {
                primary: '#10B981',
                secondary: '#FFFFFF',
              },
            },
            error: {
              iconTheme: {
                primary: '#EF4444',
                secondary: '#FFFFFF',
              },
            },
          }}
        />

        {/* Header */}
        <Header
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          sidebarOpen={sidebarOpen}
        />

        {/* Main content */}
        <div className="flex h-[calc(100vh-theme(spacing.16))]">
          {/* Main area */}
          <div className="flex-1 flex flex-col lg:mr-80">
            {/* Chart section */}
            <div className="flex-1 p-4">
              <Chart className="h-full" />
            </div>

            {/* Trading panel */}
            <div className="h-auto p-4 pt-0">
              <TradingPanel />
            </div>
          </div>

          {/* Sidebar */}
          <Sidebar
            isOpen={sidebarOpen}
            onClose={() => setSidebarOpen(false)}
          />
        </div>

        {/* Background decoration */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-green-600 rounded-full mix-blend-multiply filter blur-xl opacity-5 animate-pulse delay-2000"></div>
        </div>

        {/* Demo/Real mode indicator and toggle */}
        <div className="fixed top-20 left-4 z-30 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 ${usingDemoMode ? 'bg-orange-500' : 'bg-green-500'} rounded-full animate-pulse`}></div>
            <span className="text-sm font-medium">{usingDemoMode ? 'DEMO MODE' : 'LIVE MODE'}</span>
          </div>
          <p className="text-xs opacity-90 mt-1">
            {usingDemoMode ? 'Using simulated market data' : 'Connected to Deriv API'}
          </p>
          <button 
            onClick={() => setUsingDemoMode(!usingDemoMode)}
            className="mt-2 text-xs py-1 px-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded transition-all">
            Switch to {usingDemoMode ? 'Live' : 'Demo'} Mode
          </button>
        </div>

        {/* Mobile trading button */}
        <div className="lg:hidden fixed bottom-6 right-6 z-30">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="w-14 h-14 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full shadow-lg flex items-center justify-center text-white hover:shadow-xl transition-all transform hover:scale-105"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}

export default App;
